
# import cv2
# import uvicorn
# from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect
# from fastapi.responses import HTMLResponse
# from fastapi.staticfiles import StaticFiles
# from fastapi.middleware.cors import CORSMiddleware
# import asyncio
# import numpy as np
# import uuid
# import time
# from starlette.websockets import WebSocketState
# import csv
# import os
# import csv
# from yolo_processor import YOLOProcessor
# import threading
# from attendance_tracker import AttendanceTracker



# class WebcamStreamer:
#     """
#     A class to manage a real-time webcam streaming server with YOLOv8 object detection.
#     Loads YOLO model from a local file without downloading.
#     """

#     def __init__(self, host="127.0.0.1", port=8000, max_clients=4, batch_size=10, 
#                  max_queue_size_input=2, max_queue_size_output=2, model_path='models/yolov8n.pt'):
#         """
#         Initialize the WebcamStreamer with configuration parameters.

#         Args:
#             host (str): Host address for the FastAPI server.
#             port (int): Port for the FastAPI server.
#             max_clients (int): Maximum number of concurrent clients.
#             batch_size (int): Batch size for YOLO processing.
#             max_queue_size_input (int): Maximum size of input queue per client.
#             max_queue_size_output (int): Maximum size of output queue per client.
#             model_path (str): Path to the local YOLO model file (e.g., 'models/yolov8n.pt').
#         """
#         if not os.path.exists(model_path):
#             raise FileNotFoundError(f"YOLO model file not found at {model_path}. Please ensure the file exists.")
        
#         self.app = FastAPI()
#         self.host = host
#         self.port = port
#         self.max_clients = max_clients
#         self.batch_size = batch_size
#         self.max_queue_size_input = max_queue_size_input
#         self.max_queue_size_output = max_queue_size_output
#         self.client_queues = {}
#         self.client_queues_lock = asyncio.Lock()
#         self.global_processor_running = True
#         self.yolo_processor = YOLOProcessor(model_path=model_path)
#         self.attendance_tracker = AttendanceTracker()

#         # Add CORS middleware
#         self.app.add_middleware(
#             CORSMiddleware,
#             allow_origins=["*"],
#             allow_credentials=True,
#             allow_methods=["*"],
#             allow_headers=["*"],
#         )

#         # Mount static files
#         self.app.mount("/static", StaticFiles(directory="static"), name="static")

#         # Define HTML content with dynamic WebSocket URL and enhanced error logging
#         self.HTML_CONTENT = """
#         <!DOCTYPE html>
#         <html>
#         <head>
#             <title>Webcam Stream with YOLOv8 (Client-side)</title>
#             <style>
#                 body { font-family: Arial, sans-serif; display: flex; flex-direction: column; align-items: center; background-color: #f0f0f0; margin: 0; padding: 20px; }
#                 h1 { color: #333; }
#                 .video-container { display: flex; gap: 20px; margin-top: 20px; }
#                 video, img { border: 2px solid #ccc; background-color: #eee; }
#                 .controls { margin-top: 20px; }
#                 button { padding: 10px 20px; font-size: 16px; cursor: pointer; }
#                 .log-container { margin-top: 20px; background-color: #fff; padding: 10px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); width: 80%; max-width: 1200px; height: 150px; overflow-y: scroll; border: 1px solid #ddd;}
#                 pre { white-space: pre-wrap; word-wrap: break-word; }
#             </style>
#             <link rel="stylesheet" href="/static/styles.css"> 
#         </head>
#         <body>
#             <h1>Real-Time Object Detection (Client Webcam)</h1>
#             <div class="video-container">
#                 <div>
#                     <h2>Your Webcam (Raw)</h2>
#                     <video id="webcamVideo" width="640" height="480" autoplay muted></video>
#                 </div>
#                 <div>
#                     <h2>Processed Output (YOLOv8)</h2>
#                     <img id="processedImage" width="640" height="480">
#                 </div>
#                 <canvas id="webcamCanvas" width="640" height="480" style="display: none;"></canvas>
#             </div>
#             <div class="controls">
#                 <button id="startButton">Start Stream</button>
#                 <button id="stopButton">Stop Stream</button>
#             </div>
#             <div class="log-container">
#                 <pre id="log"></pre>
#             </div>

#             <script>
#                 const videoElement = document.getElementById('webcamVideo');
#                 const canvasElement = document.getElementById('webcamCanvas');
#                 const processedImageElement = document.getElementById('processedImage');
#                 const startButton = document.getElementById('startButton');
#                 const stopButton = document.getElementById('stopButton');
#                 const logElement = document.getElementById('log');
#                 const canvasContext = canvasElement.getContext('2d');
#                 let ws;
#                 let webcamStream;
#                 let sendFrameIntervalId;
#                 const targetFPS = 30;
#                 const sendFrameRate = 1000 / targetFPS;

#                 function log(message) {
#                     const now = new Date().toLocaleTimeString();
#                     logElement.textContent += `[${now}] ${message}\n`;
#                     logElement.scrollTop = logElement.scrollHeight;
#                 }

#                 function startStream() {
#                     log("Starting stream...");
#                     if (ws && ws.readyState === WebSocket.OPEN) {
#                         log("Stream already active.");
#                         return;
#                     }

#                     if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
#                         log("WebRTC not supported.");
#                         alert("WebRTC not supported.");
#                         return;
#                     }

#                     log("Requesting webcam access...");
#                     navigator.mediaDevices.getUserMedia({ 
#                         video: {
#                             width: { ideal: 640 },
#                             height: { ideal: 480 },
#                             frameRate: { ideal: targetFPS }
#                         }
#                     })
#                     .then(function(stream) {
#                         log("Webcam access granted.");
#                         webcamStream = stream;
#                         videoElement.srcObject = stream;
#                         videoElement.onloadedmetadata = () => {
#                             log(`Webcam resolution: ${videoElement.videoWidth}x${videoElement.videoHeight}`);
#                             canvasElement.width = videoElement.videoWidth;
#                             canvasElement.height = videoElement.videoHeight;
#                             processedImageElement.width = videoElement.videoWidth;
#                             processedImageElement.height = videoElement.videoHeight;
#                         };

#                         const wsProtocol = window.location.protocol === "https:" ? "wss:" : "ws:";
#                         const wsHost = window.location.host; // Use host to include port if non-standard
#                         const wsUrl = `${wsProtocol}//${wsHost}/ws/webcam_stream`;
#                         log(`Connecting to WebSocket at ${wsUrl}...`);
                        
#                         try {
#                             ws = new WebSocket(wsUrl);
#                             ws.binaryType = 'arraybuffer';

#                             ws.onopen = () => {
#                                 log("WebSocket connected.");
#                                 sendFrameIntervalId = setInterval(sendFrame, sendFrameRate);
#                                 startButton.disabled = true;
#                                 stopButton.disabled = false;
#                             };

#                             ws.onmessage = (event) => {
#                                 log("Received processed frame.");
#                                 const blob = new Blob([event.data], { type: 'image/jpeg' });
#                                 const url = URL.createObjectURL(blob);
#                                 if (processedImageElement.src && processedImageElement.src.startsWith('blob:')) {
#                                     URL.revokeObjectURL(processedImageElement.src);
#                                 }
#                                 processedImageElement.src = url;
#                             };

#                             ws.onclose = (event) => {
#                                 log(`WebSocket closed. Code: ${event.code}, Reason: ${event.reason}`);
#                                 stopSendingFrames();
#                                 startButton.disabled = false;
#                                 stopButton.disabled = true;
#                             };

#                             ws.onerror = (error) => {
#                                 log("WebSocket error: " + (error.message || error));
#                                 console.error("WebSocket error:", error);
#                                 ws.close();
#                             };
#                         } catch (e) {
#                             log(`WebSocket initialization failed: ${e.message}`);
#                             console.error("WebSocket init error:", e);
#                         }
#                     })
#                     .catch(function(error) {
#                         log(`Webcam access failed: ${error.name} - ${error.message}`);
#                         console.error("Webcam error:", error);
#                         alert(`Webcam access failed: ${error.name} - ${error.message}`);
#                     });
#                 }

#                 function sendFrame() {
#                     if (ws && ws.readyState === WebSocket.OPEN && videoElement.videoWidth > 0 && videoElement.readyState >= 2) {
#                         canvasContext.drawImage(videoElement, 0, 0, canvasElement.width, canvasElement.height);
#                         canvasElement.toBlob(function(blob) {
#                             if (blob) {
#                                 const reader = new FileReader();
#                                 reader.onload = function() {
#                                     if (ws && ws.readyState === WebSocket.OPEN) {
#                                         ws.send(reader.result);
#                                     }
#                                 };
#                                 reader.onerror = function(error) {
#                                     log(`Error reading blob: ${error}`);
#                                 };
#                                 reader.readAsArrayBuffer(blob);
#                             }
#                         }, 'image/jpeg', 0.7);
#                     }
#                 }

#                 function stopSendingFrames() {
#                     if (sendFrameIntervalId) {
#                         clearInterval(sendFrameIntervalId);
#                         sendFrameIntervalId = null;
#                         log("Stopped sending frames.");
#                     }
#                     if (webcamStream) {
#                         webcamStream.getTracks().forEach(track => track.stop());
#                         webcamStream = null;
#                         videoElement.srcObject = null;
#                         log("Webcam stream stopped.");
#                     }
#                     if (ws) {
#                         if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
#                             ws.close();
#                         }
#                         ws = null;
#                         log("WebSocket client closed.");
#                     }
#                 }

#                 startButton.addEventListener('click', startStream);
#                 stopButton.addEventListener('click', stopSendingFrames);
#                 startButton.disabled = false;
#                 stopButton.disabled = true;
#             </script>
#         </body>
#         </html>
#         """

#         # Setup routes and event handlers
#         self._setup_routes()

#     def _setup_routes(self):
#         """Setup FastAPI routes and WebSocket endpoints."""
#         @self.app.get("/", response_class=HTMLResponse)
#         async def get_index():
#             return self.HTML_CONTENT

#         @self.app.on_event("startup")
#         async def startup_event():
#             asyncio.create_task(self._frame_processor_task())

#         @self.app.on_event("shutdown")
#         async def shutdown_event():
#             self.global_processor_running = False
#             print("FastAPI application shutting down.")

#         @self.app.websocket("/ws/webcam_stream")
#         async def websocket_webcam_stream(websocket: WebSocket):
#             session_id = None
#             receive_task = None
#             send_task = None
#             try:
#                 async with self.client_queues_lock:
#                     if len(self.client_queues) >= self.max_clients:
#                         await websocket.close(code=1013, reason=f"Quá nhiều client: {self.max_clients}")
#                         return
#                 await websocket.accept()
#                 session_id = str(uuid.uuid4())
#                 print(f"[WebSocket] Client kết nối: /ws/webcam_stream, session_id: {session_id}")

#                 async with self.client_queues_lock:
#                     self.client_queues[session_id] = {
#                         "input": asyncio.Queue(maxsize=self.max_queue_size_input),
#                         "output": asyncio.Queue(maxsize=self.max_queue_size_output)
#                     }

#                 receive_task = asyncio.create_task(self._receive_frames(websocket, session_id))
#                 send_task = asyncio.create_task(self._send_processed_frames(websocket, session_id))
#                 await asyncio.gather(receive_task, send_task)

#             except WebSocketDisconnect as e:
#                 print(f"[WebSocket] Client {session_id} ngắt kết nối: {e.code} - {e.reason}")
#             finally:
#                 async with self.client_queues_lock:
#                     if session_id in self.client_queues:
#                         del self.client_queues[session_id]
#                         print(f"[WebSocket] Dọn dẹp hàng đợi cho session {session_id}")
#                 if receive_task:
#                     receive_task.cancel()
#                 if send_task:
#                     send_task.cancel()
#                 if websocket.client_state == WebSocketState.CONNECTED:
#                     try:
#                         await websocket.close(code=1000, reason="Đóng bình thường")
#                     except Exception as e:
#                         print(f"[WebSocket] Lỗi đóng WebSocket cho session {session_id}: {e}")

#     async def _frame_processor_task(self):
#         """Background task to process frames from all clients using YOLOProcessor."""
#         print("[Processor] Thread started, waiting for frames from clients.")
#         process_frame_count = 0
#         process_start_time = time.time()

#         while self.global_processor_running:
#             frames_to_process = []
#             client_session_ids = []

#             async with self.client_queues_lock:
#                 for session_id in list(self.client_queues.keys()):
#                     input_queue = self.client_queues[session_id]["input"]
#                     if not input_queue.empty():
#                         while input_queue.qsize() > self.max_queue_size_input:
#                             await input_queue.get()

#                         try:
#                             frame_data_bytes = await asyncio.wait_for(input_queue.get(), timeout=0.001)
#                             np_arr = np.frombuffer(frame_data_bytes, np.uint8)
#                             frame_decoded = cv2.imdecode(np_arr, cv2.IMREAD_COLOR)

#                             if frame_decoded is not None:
#                                 frames_to_process.append(frame_decoded)
#                                 client_session_ids.append(session_id)
#                             else:
#                                 print(f"[Processor] Could not decode frame for session {session_id}. Skipping.")
#                         except asyncio.TimeoutError:
#                             pass
#                         except Exception as e:
#                             print(f"[Processor] Error getting frame for session {session_id}: {e}")
#                             import traceback
#                             traceback.print_exc()

#             if frames_to_process:
#                 try:
#                     processed_frames_batch = self.yolo_processor.process_frames(frames_to_process)
                    
#                     for idx, processed_frame in enumerate(processed_frames_batch):
#                         session_id = client_session_ids[idx]
                        
#                         if session_id in self.client_queues:
#                             output_queue = self.client_queues[session_id]["output"]
#                             ret_proc, buffer_proc = cv2.imencode('.jpg', processed_frame, [int(cv2.IMWRITE_JPEG_QUALITY), 70])

#                             if ret_proc:
#                                 try:
#                                     while output_queue.qsize() >= self.max_queue_size_output:
#                                         output_queue.get_nowait()
#                                     output_queue.put_nowait(buffer_proc.tobytes())
#                                 except asyncio.QueueFull:
#                                     pass
#                                 except Exception as e:
#                                     print(f"[Processor] Error putting frame to output queue for session {session_id}: {e}")
#                             else:
#                                 print(f"[Processor] Could not encode processed frame for session {session_id}.")

#                     process_frame_count += len(frames_to_process)
#                     if time.time() - process_start_time >= 1.0:
#                         print(f"[Processor] Processed {process_frame_count} frames in 1s. FPS: {process_frame_count / (time.time() - process_start_time):.2f}")
#                         process_frame_count = 0
#                         process_start_time = time.time()
#                 except Exception as e:
#                     print(f"[Processor] Error processing batch: {e}")
#                     import traceback
#                     traceback.print_exc()

#             await asyncio.sleep(0.001)

#     async def _receive_frames(self, websocket: WebSocket, session_id: str):
#         """Handle receiving frames from a client."""
#         while websocket.client_state == WebSocketState.CONNECTED:
#             try:
#                 frame_bytes_from_client = await asyncio.wait_for(websocket.receive_bytes(), timeout=0.05)
#                 async with self.client_queues_lock:
#                     if session_id in self.client_queues:
#                         input_queue = self.client_queues[session_id]["input"]
#                         try:
#                             if input_queue.qsize() >= self.max_queue_size_input:
#                                 await input_queue.get_nowait()
#                             input_queue.put_nowait(frame_bytes_from_client)
#                         except asyncio.QueueFull:
#                             pass
#                         except Exception as e:
#                             print(f"[Receive Task] Error putting frame to input queue for session {session_id}: {e}")
#                             break
#             except asyncio.TimeoutError:
#                 pass
#             except (WebSocketDisconnect, RuntimeError) as e:
#                 print(f"[Receive Task] Client {session_id} disconnected or runtime error: {e}")
#                 break
#             except Exception as e:
#                 print(f"[Receive Task] Unexpected error receiving from client {session_id}: {e}")
#                 import traceback
#                 traceback.print_exc()
#                 break

#     async def _send_processed_frames(self, websocket: WebSocket, session_id: str):
#         """Handle sending processed frames to a client."""
#         while websocket.client_state == WebSocketState.CONNECTED:
#             try:
#                 async with self.client_queues_lock:
#                     if session_id in self.client_queues:
#                         output_queue = self.client_queues[session_id]["output"]
#                         processed_bytes_to_client = await asyncio.wait_for(output_queue.get(), timeout=0.05)
#                         await websocket.send_bytes(processed_bytes_to_client)
#             except asyncio.TimeoutError:
#                 pass
#             except (WebSocketDisconnect, RuntimeError) as e:
#                 print(f"[Send Task] Client {session_id} disconnected or runtime error: {e}")
#                 break
#             except Exception as e:
#                 print(f"[Send Task] Unexpected error sending to client {session_id}: {e}")
#                 import traceback
#                 traceback.print_exc()
#                 break

#     def run(self):
#         config = uvicorn.Config(self.app, host=self.host, port=self.port)
#         server = uvicorn.Server(config)
#         local_url = f"http://{self.host}:{self.port}"
#         public_url = local_url  # Mặc định public_url là local_url nếu không có ngrok
#         try:
#             from pyngrok import ngrok
#             public_url = ngrok.connect(self.port, bind_tls=True).public_url
#             print("FastAPI Public URL (ngrok):", public_url)
#         except ImportError:
#             print("Không cài ngrok, chạy FastAPI cục bộ tại:", local_url)

#         uvicorn_thread = threading.Thread(target=server.run)
#         uvicorn_thread.daemon = True
#         uvicorn_thread.start()
#         time.sleep(1)
#         return public_url, local_url

# if __name__ == "__main__":
#     streamer = WebcamStreamer()
#     streamer.run()

import cv2
import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import asyncio
import numpy as np
import uuid
import time
from starlette.websockets import WebSocketState
import threading
from yolo_processor import YOLOProcessor
from attendance_tracker import AttendanceTracker
import os
from contextlib import asynccontextmanager

# Khai báo ở đầu file
global_processor_running = True

@asynccontextmanager
async def lifespan(app: FastAPI):
    global global_processor_running  # ✅ Thêm dòng này
    global_processor_running = True
    task = asyncio.create_task(frame_processor_task())
    yield
    global_processor_running = False
    task.cancel()  # ✅ Cancel task khi shutdown



app = FastAPI(lifespan=lifespan)
host = "127.0.0.1"
port = 8080
max_clients = 4
batch_size = 10
max_queue_size_input = 5
max_queue_size_output = 5
model_path = 'models/yolov8n.pt'

if not os.path.exists(model_path):
    raise FileNotFoundError(f"YOLO model file not found at {model_path}")

client_queues = {}
client_queues_lock = asyncio.Lock()
global_processor_running = True
yolo_processor = YOLOProcessor(model_path=model_path)
attendance_tracker = AttendanceTracker()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"]
)
@app.get("/health")
async def health_check():
    return {
        "status": "healthy", 
        "websocket_clients": len(client_queues),
        "processor_running": global_processor_running
    }

@app.get("/api/attendance", response_class=JSONResponse)
async def get_attendance():
    try:
        attendance_log = attendance_tracker.get_attendance_log()
        return JSONResponse(content=attendance_log)
    except Exception as e:
        print(f"Error retrieving attendance log: {e}")
        return JSONResponse(content=[], status_code=500)

@app.websocket("/ws/webcam_stream")
async def websocket_webcam_stream(websocket: WebSocket):
    session_id = None
    receive_task = None
    send_task = None
    try:
        print(f"[WebSocket] Received connection attempt from {websocket.client.host}")
        async with client_queues_lock:
            if len(client_queues) >= max_clients:
                await websocket.close(code=1013, reason=f"Too many clients: {max_clients}")
                return
        await websocket.accept()
        session_id = str(uuid.uuid4())
        print(f"[WebSocket] Client connected: /ws/webcam_stream, session_id: {session_id}, IP: {websocket.client.host}")

        async with client_queues_lock:
            client_queues[session_id] = {
                "input": asyncio.Queue(maxsize=max_queue_size_input),
                "output": asyncio.Queue(maxsize=max_queue_size_output)
            }

        receive_task = asyncio.create_task(receive_frames(websocket, session_id))
        send_task = asyncio.create_task(send_processed_frames(websocket, session_id))
        await asyncio.gather(receive_task, send_task)

    except WebSocketDisconnect as e:
        print(f"[WebSocket] Client {session_id} disconnected: {e.code} - {e.reason}")
    except Exception as e:
        print(f"[WebSocket] Unexpected error for session {session_id}: {e}")
    finally:
        async with client_queues_lock:
            if session_id in client_queues:
                del client_queues[session_id]
                print(f"[WebSocket] Cleaned up queues for session {session_id}")
        if receive_task:
            receive_task.cancel()
        if send_task:
            send_task.cancel()
        if websocket.client_state == WebSocketState.CONNECTED:
            try:
                await websocket.close(code=1000, reason="Normal closure")
            except Exception as e:
                print(f"[WebSocket] Error closing WebSocket for session {session_id}: {e}")

async def frame_processor_task():
    print("[Processor] Thread started, waiting for frames from clients.")
    process_frame_count = 0
    process_start_time = time.time()

    while global_processor_running:
        frames_to_process = []
        client_session_ids = []

        async with client_queues_lock:
            for session_id in list(client_queues.keys()):
                input_queue = client_queues[session_id]["input"]
                if not input_queue.empty():
                    while input_queue.qsize() > 1:  # Giữ lại ít nhất 1 frame để tránh tràn
                        await input_queue.get()

                    try:
                        frame_data_bytes = await asyncio.wait_for(input_queue.get(), timeout=1.0)
                        np_arr = np.frombuffer(frame_data_bytes, np.uint8)
                        frame_decoded = cv2.imdecode(np_arr, cv2.IMREAD_COLOR)

                        if frame_decoded is not None:
                            frames_to_process.append(frame_decoded)
                            client_session_ids.append(session_id)
                        else:
                            print(f"[Processor] Could not decode frame for session {session_id}. Skipping.")
                    except asyncio.TimeoutError:
                        continue
                    except Exception as e:
                        print(f"[Processor] Error getting frame for session {session_id}: {e}")
                        import traceback
                        traceback.print_exc()

        if frames_to_process:
            try:
                processed_frames_batch = yolo_processor.process_frames(frames_to_process)
                
                for idx, processed_frame in enumerate(processed_frames_batch):
                    session_id = client_session_ids[idx]
                    results = yolo_processor.model(processed_frame, verbose=False)
                    detections = [
                        {
                            "class_name": yolo_processor.model.names[int(cls)],
                            "confidence": conf,
                            "box": box.xyxy[0].cpu().numpy()
                        }
                        for box, cls, conf in zip(results[0].boxes, results[0].boxes.cls, results[0].boxes.conf)
                        if results[0].boxes is not None
                    ]
                    attendance_tracker.track_objects(detections, session_id)

                for idx, processed_frame in enumerate(processed_frames_batch):
                    session_id = client_session_ids[idx]
                    if session_id in client_queues:
                        output_queue = client_queues[session_id]["output"]
                        ret_proc, buffer_proc = cv2.imencode('.jpg', processed_frame, [int(cv2.IMWRITE_JPEG_QUALITY), 80])

                        if ret_proc:
                            try:
                                if output_queue.qsize() >= max_queue_size_output:
                                    output_queue.get_nowait()
                                output_queue.put_nowait(buffer_proc.tobytes())
                            except asyncio.QueueFull:
                                print(f"[Processor] Output queue full for session {session_id}, dropping frame")
                            except Exception as e:
                                print(f"[Processor] Error putting frame to output queue for session {session_id}: {e}")
                        else:
                            print(f"[Processor] Could not encode processed frame for session {session_id}.")

                process_frame_count += len(frames_to_process)
                if time.time() - process_start_time >= 1.0:
                    print(f"[Processor] Processed {process_frame_count} frames in 1s. FPS: {process_frame_count / (time.time() - process_start_time):.2f}")
                    process_frame_count = 0
                    process_start_time = time.time()
            except Exception as e:
                print(f"[Processor] Error processing batch: {e}")
                import traceback
                traceback.print_exc()

        await asyncio.sleep(0.01)

async def receive_frames(websocket: WebSocket, session_id: str):
    while websocket.client_state == WebSocketState.CONNECTED:
        try:
            frame_bytes_from_client = await asyncio.wait_for(websocket.receive_bytes(), timeout=1.0)
            async with client_queues_lock:
                if session_id in client_queues:
                    input_queue = client_queues[session_id]["input"]
                    try:
                        if input_queue.qsize() >= max_queue_size_input:
                            await input_queue.get()
                        input_queue.put_nowait(frame_bytes_from_client)
                        print(f"[Receive Task] Received frame for session {session_id}, queue size: {input_queue.qsize()}")
                    except asyncio.QueueFull:
                        print(f"[Receive Task] Input queue full for session {session_id}, dropping frame")
                    except Exception as e:
                        print(f"[Receive Task] Error putting frame to input queue for session {session_id}: {e}")
                        break
        except asyncio.TimeoutError:
            continue
        except (WebSocketDisconnect, RuntimeError) as e:
            print(f"[Receive Task] Client {session_id} disconnected or runtime error: {e}")
            break
        except Exception as e:
            print(f"[Receive Task] Unexpected error receiving from client {session_id}: {e}")
            import traceback
            traceback.print_exc()
            break

async def send_processed_frames(websocket: WebSocket, session_id: str):
    while websocket.client_state == WebSocketState.CONNECTED:
        try:
            async with client_queues_lock:
                if session_id in client_queues:
                    output_queue = client_queues[session_id]["output"]
                    processed_bytes_to_client = await asyncio.wait_for(output_queue.get(), timeout=1.0)
                    await websocket.send_bytes(processed_bytes_to_client)
                    print(f"[Send Task] Sent processed frame to session {session_id}, queue size: {output_queue.qsize()}")
        except asyncio.TimeoutError:
            continue
        except (WebSocketDisconnect, RuntimeError) as e:
            print(f"[Send Task] Client {session_id} disconnected or runtime error: {e}")
            break
        except Exception as e:
            print(f"[Send Task] Unexpected error sending to client {session_id}: {e}")
            import traceback
            traceback.print_exc()
            break

def run():
    config = uvicorn.Config(app, host=host, port=port)
    server = uvicorn.Server(config)
    local_url = f"http://{host}:{port}"
    public_url = local_url

    try:
        from pyngrok import ngrok
        public_url = ngrok.connect(port, bind_tls=True).public_url
        print("FastAPI Public URL (ngrok):", public_url)
    except ImportError:
        print("Ngrok not installed, running FastAPI locally at:", local_url)

    uvicorn_thread = threading.Thread(target=server.run)
    uvicorn_thread.daemon = True
    uvicorn_thread.start()
    time.sleep(1)
    return public_url, local_url

if __name__ == "__main__":
    print("Starting FastAPI backend...")
    public_url, local_url = run()
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("Shutting down FastAPI backend...")