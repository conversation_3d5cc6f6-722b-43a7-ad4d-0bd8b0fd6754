import gradio as gr
import requests
import pandas as pd
from urllib3.exceptions import MaxRetryError, NewConnectionError

# Backend API base URL
BACKEND_URL = "http://127.0.0.1:8080"

def check_backend_status():
    try:
        response = requests.get(f"{BACKEND_URL}/api/attendance", timeout=3)
        return response.status_code == 200
    except (requests.ConnectionError, MaxRetryError, NewConnectionError):
        return False

def get_attendance_log():
    try:
        response = requests.get(f"{BACKEND_URL}/api/attendance", timeout=5)
        response.raise_for_status()
        data = response.json()
        if not data or not isinstance(data, list):
            print("No attendance data received or invalid format")
            return []
        return [[record.get("name", ""), record.get("timestamp", "")] for record in data]
    except (requests.ConnectionError, MaxRetryError, NewConnectionError) as e:
        print(f"Error fetching attendance: Backend server not running at {BACKEND_URL}. Please run 'python video_streaming.py' first. {e}")
        return []
    except requests.RequestException as e:
        print(f"Error fetching attendance log: {e}")
        return []

def update_attendance_table():
    data = get_attendance_log()
    if not data:
        return data, "Chưa có dữ liệu attendance hoặc backend chưa phát hiện đối tượng."
    return data, ""

def run_gradio():
    backend_available = check_backend_status()

    with gr.Blocks() as demo:
        if not backend_available:
            gr.Markdown("**Cảnh báo: Máy chủ backend chưa chạy. Vui lòng chạy 'python video_streaming.py' trong terminal khác trước.**")

        gr.Markdown("# 🎥 YOLOv8 Webcam Stream + Attendance Log")

        with gr.Row():
            with gr.Column(scale=3):
                STREAMING_HTML = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Webcam Stream with YOLOv8 (Client-side)</title>
                    <style>
                        body { font-family: Arial, sans-serif; display: flex; flex-direction: column; align-items: center; background-color: #f0f0f0; margin: 0; padding: 20px; }
                        h1 { color: #333; }
                        .video-container { display: flex; gap: 20px; margin-top: 20px; }
                        video, img { border: 2px solid #ccc; background-color: #eee; }
                        .controls { margin-top: 20px; }
                        button { padding: 10px 20px; font-size: 16px; cursor: pointer; }
                        .log-container { margin-top: 20px; background-color: #fff; padding: 10px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); width: 80%; max-width: 1200px; height: 200px; overflow-y: scroll; border: 1px solid #ddd; }
                        pre { white-space: pre-wrap; word-wrap: break-word; }
                    </style>
                </head>
                <body>
                    <h1>Real-Time Object Detection (Client Webcam)</h1>
                    <div class="video-container">
                        <div>
                            <h2>Your Webcam (Raw)</h2>
                            <video id="webcamVideo" width="640" height="480" autoplay muted></video>
                        </div>
                        <div>
                            <h2>Processed Output (YOLOv8)</h2>
                            <img id="processedImage" width="640" height="480">
                        </div>
                        <canvas id="webcamCanvas" width="640" height="480" style="display: none;"></canvas>
                    </div>
                    <div class="controls">
                        <button id="startButton">Start Stream</button>
                        <button id="stopButton">Stop Stream</button>
                    </div>
                    <div class="log-container">
                        <pre id="log"></pre>
                    </div>

                    <script>
                        const videoElement = document.getElementById('webcamVideo');
                        const canvasElement = document.getElementById('webcamCanvas');
                        const processedImageElement = document.getElementById('processedImage');
                        const startButton = document.getElementById('startButton');
                        const stopButton = document.getElementById('stopButton');
                        const logElement = document.getElementById('log');
                        const canvasContext = canvasElement.getContext('2d');
                        let ws = null;
                        let webcamStream = null;
                        let sendFrameIntervalId = null;
                        const targetFPS = 15;
                        const sendFrameRate = 1000 / targetFPS;

                        function log(message) {
                            const now = new Date().toLocaleTimeString();
                            logElement.textContent += `[${now}] ${message}\n`;
                            logElement.scrollTop = logElement.scrollHeight;
                            console.log(`[Log] ${message}`);
                        }

                        function startStream() {
                            log("Starting stream process...");
                            if (ws && (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING)) {
                                log("Stream already active or connecting, skipping.");
                                return;
                            }

                            // Attempt WebSocket connection first
                            const wsUrl = `ws://127.0.0.1:8080/ws/webcam_stream`;
                            log(`Attempting WebSocket connection to ${wsUrl}...`);
                            ws = new WebSocket(wsUrl);
                            ws.binaryType = 'arraybuffer';

                            ws.onopen = () => {
                                log("WebSocket connected successfully.");
                                startButton.disabled = true;
                                stopButton.disabled = false;

                                // Proceed with webcam only after WebSocket is open
                                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                                    log("WebRTC not supported by browser.");
                                    alert("WebRTC not supported by your browser.");
                                    return;
                                }

                                log("Requesting webcam access...");
                                navigator.mediaDevices.getUserMedia({
                                    video: { width: { ideal: 640 }, height: { ideal: 480 }, frameRate: { ideal: targetFPS } }
                                }).then(stream => {
                                    log("Webcam access granted.");
                                    webcamStream = stream;
                                    videoElement.srcObject = stream;
                                    videoElement.onloadedmetadata = () => {
                                        log(`Webcam resolution: ${videoElement.videoWidth}x${videoElement.videoHeight}`);
                                        canvasElement.width = videoElement.videoWidth;
                                        canvasElement.height = videoElement.videoHeight;
                                        processedImageElement.width = videoElement.videoWidth;
                                        processedImageElement.height = videoElement.videoHeight;
                                        sendFrameIntervalId = setInterval(sendFrame, sendFrameRate);
                                    };
                                }).catch(error => {
                                    log(`Webcam access failed: ${error.name} - ${error.message}`);
                                    alert(`Webcam access failed: ${error.name} - ${error.message}`);
                                });
                            };

                            ws.onmessage = (event) => {
                                log("Received processed frame.");
                                const blob = new Blob([event.data], { type: 'image/jpeg' });
                                const url = URL.createObjectURL(blob);
                                if (processedImageElement.src && processedImageElement.src.startsWith('blob:')) {
                                    URL.revokeObjectURL(processedImageElement.src);
                                }
                                processedImageElement.src = url;
                            };

                            ws.onclose = (event) => {
                                log(`WebSocket closed. Code: ${event.code}, Reason: ${event.reason}`);
                                stopSendingFrames();
                            };

                            ws.onerror = (error) => {
                                log(`WebSocket error: ${error.message || 'Unknown error'}`);
                                console.error("WebSocket error:", error);
                                if (ws) ws.close();
                            };
                        }

                        function sendFrame() {
                            if (!ws || ws.readyState !== WebSocket.OPEN) {
                                log("Cannot send frame: WebSocket not open.");
                                return;
                            }
                            if (!webcamStream || videoElement.videoWidth <= 0 || videoElement.readyState < 2) {
                                log("Cannot send frame: Video not ready.");
                                return;
                            }

                            canvasContext.drawImage(videoElement, 0, 0, canvasElement.width, canvasElement.height);
                            canvasElement.toBlob(blob => {
                                if (blob) {
                                    const reader = new FileReader();
                                    reader.onload = () => {
                                        if (ws && ws.readyState === WebSocket.OPEN) {
                                            ws.send(reader.result);
                                            log("Frame sent to server.");
                                        } else {
                                            log("Frame not sent: WebSocket not open.");
                                        }
                                    };
                                    reader.onerror = error => log(`Error reading blob: ${error}`);
                                    reader.readAsArrayBuffer(blob);
                                } else {
                                    log("Failed to create blob from canvas.");
                                }
                            }, 'image/jpeg', 0.7);
                        }

                        function stopSendingFrames() {
                            if (sendFrameIntervalId) {
                                clearInterval(sendFrameIntervalId);
                                sendFrameIntervalId = null;
                                log("Stopped sending frames.");
                            }
                            if (webcamStream) {
                                webcamStream.getTracks().forEach(track => track.stop());
                                webcamStream = null;
                                videoElement.srcObject = null;
                                log("Webcam stream stopped.");
                            }
                            if (ws) {
                                if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
                                    ws.close();
                                }
                                ws = null;
                                log("WebSocket client closed.");
                            }
                            startButton.disabled = false;
                            stopButton.disabled = true;
                        }

                        startButton.addEventListener('click', startStream);
                        stopButton.addEventListener('click', stopSendingFrames);
                        startButton.disabled = false;
                        stopButton.disabled = true;
                    </script>
                </body>
                </html>
                """
                gr.HTML(STREAMING_HTML)
            with gr.Column(scale=1):
                gr.Markdown("## 📋 Attendance Log")
                attendance_table = gr.Dataframe(
                    headers=["Name", "Timestamp"],
                    datatype=["str", "str"],
                    row_count=10,
                    col_count=2,
                    interactive=False,
                    wrap=True,
                    elem_id="attendance-table"
                )
                status_msg = gr.Markdown()
                refresh_btn = gr.Button("🔄 Refresh")
                timer = gr.Timer(value=2.0)

                refresh_btn.click(
                    fn=update_attendance_table,
                    outputs=[attendance_table, status_msg]
                )
                timer.tick(
                    fn=update_attendance_table,
                    outputs=[attendance_table, status_msg]
                )

        demo.css = """
        #attendance-table {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
        }
        #attendance-table table {
            width: 100%;
            border-collapse: collapse;
        }
        #attendance-table th, #attendance-table td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        """

    demo.launch(
        server_name="127.0.0.1",
        server_port=7860,
        share=False,
        debug=True
    )

if __name__ == "__main__":
    print("Starting Gradio frontend...")
    run_gradio()